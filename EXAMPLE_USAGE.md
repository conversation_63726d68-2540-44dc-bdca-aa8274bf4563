# Ví dụ sử dụng Search Engine

## Cách chạy chương trình

### Cách 1: Sử dụng script
```bash
./run.sh
```

### Cách 2: Ch<PERSON>y thủ công
```bash
# Biên dịch
javac -d . src/search_engine/*.java

# Chạy chương trình
java search_engine.Search_Engine
```

## Ví dụ sử dụng thực tế

### Bước 1: Khởi động chương trình
```
=== SEARCH ENGINE ===
1. Nhập địa chỉ website và độ sâu tìm kiếm
2. Nhập từ khóa cần tìm kiếm
3. Thực hiện crawl và tìm kiếm
4. Lưu kết quả vào file
5. Kết thúc chương trình
Chọn chức năng (1-5): 
```

### Bước 2: Nhập website và độ sâu
```
<PERSON><PERSON><PERSON> chứ<PERSON> năng (1-5): 1
Nhập địa chỉ website ban đầu: https://docs.oracle.com/javase/tutorial/
Nhập độ sâu tìm kiếm (1-3): 2
Đã thiết lập:
- Website: https://docs.oracle.com/javase/tutorial/
- Độ sâu: 2
```

### Bước 3: Nhập từ khóa
```
Chọn chức năng (1-5): 2
Nhập từ khóa cần tìm kiếm: java
Đã thiết lập từ khóa: java
```

### Bước 4: Thực hiện tìm kiếm
```
Chọn chức năng (1-5): 3
Bắt đầu crawl và tìm kiếm...
Đang crawl: https://docs.oracle.com/javase/tutorial/
Đang crawl: https://docs.oracle.com/javase/tutorial/java/
Đang crawl: https://docs.oracle.com/javase/tutorial/essential/

Kết quả crawl:
- Tổng số trang đã crawl: 25
- Số trang chứa từ khóa: 18
```

### Bước 5: Lưu kết quả
```
Chọn chức năng (1-5): 4
Nhập tên file để lưu kết quả (ví dụ: results.txt): java_search_results.txt
Đã lưu kết quả vào file: java_search_results.txt
Tổng số trang tìm thấy từ khóa: 18
```

## Nội dung file kết quả mẫu

File `java_search_results.txt` sẽ có nội dung như sau:

```
=== KẾT QUẢ TÌM KIẾM TỪ KHÓA: java ===
Tổng số trang tìm thấy: 18
Thời gian: Mon Oct 23 14:30:45 ICT 2023
==================================================

1. https://docs.oracle.com/javase/tutorial/java/concepts/ (Xuất hiện 45 lần)
2. https://docs.oracle.com/javase/tutorial/java/nutsandbolts/ (Xuất hiện 32 lần)
3. https://docs.oracle.com/javase/tutorial/java/javaOO/ (Xuất hiện 28 lần)
4. https://docs.oracle.com/javase/tutorial/java/data/ (Xuất hiện 21 lần)
5. https://docs.oracle.com/javase/tutorial/essential/io/ (Xuất hiện 15 lần)
...
```

## Các website gợi ý để test

### Website giáo dục:
- https://docs.oracle.com/javase/tutorial/ (từ khóa: "java", "class", "method")
- https://www.w3schools.com/ (từ khóa: "html", "css", "javascript")
- https://developer.mozilla.org/ (từ khóa: "web", "api", "javascript")

### Website tin tức công nghệ:
- https://stackoverflow.com/ (từ khóa: "programming", "code", "solution")
- https://github.com/ (từ khóa: "repository", "code", "open source")

## Lưu ý khi sử dụng

1. **Chọn độ sâu phù hợp**: 
   - Độ sâu 1: Chỉ crawl trang chính
   - Độ sâu 2: Crawl trang chính + các link trực tiếp
   - Độ sâu 3: Crawl sâu hơn (có thể mất nhiều thời gian)

2. **Chọn từ khóa hiệu quả**:
   - Sử dụng từ khóa cụ thể
   - Tránh từ khóa quá chung chung như "the", "and"
   - Có thể tìm kiếm cụm từ như "machine learning"

3. **Thời gian chờ**:
   - Quá trình crawl có thể mất vài phút tùy thuộc vào website
   - Một số website có thể chặn crawling

4. **Kết quả**:
   - Kết quả được sắp xếp theo số lần xuất hiện giảm dần
   - File kết quả được lưu trong thư mục hiện tại
