# Search Engine - C<PERSON>ng cụ tìm kiếm từ khóa trên website

## Mô tả
Chương trình Search Engine đơn giản cho phép tìm kiếm từ khóa trên các trang web. Chương trình sẽ crawl các trang web theo độ sâu được chỉ định và tìm kiếm từ khóa trong nội dung các trang.

## Tính năng
1. **Nhập địa chỉ website và độ sâu tìm kiếm**: Thiết lập website ban đầu và độ sâu crawl (1-3)
2. **Nhập từ khóa cần tìm kiếm**: Thiết lập từ khóa để tìm kiếm
3. **Thực hiện crawl và tìm kiếm**: Crawl website và tìm kiếm từ khóa
4. **Lư<PERSON> kết quả vào file**: <PERSON><PERSON><PERSON> kết quả tìm kiếm được sắp xếp theo số lần xuất hiện giảm dần
5. **Kết thúc chương trình**: Thoát khỏi chương trình

## Cấu trúc chương trình

### Các thành phần chính:
- **WebCrawler**: Thu thập các link từ website ban đầu theo độ sâu
- **Search Engine**: Tìm kiếm từ khóa trong các trang đã crawl
- **HashMap**: Lưu trữ URL và số lần xuất hiện từ khóa
- **TreeMap với Comparator**: Sắp xếp kết quả theo số lần xuất hiện giảm dần

### Các method quan trọng:
- `crawlWebsite(String startUrl, int depth)`: Crawl website theo độ sâu
- `extractLinks(String htmlContent, String baseUrl)`: Trích xuất links từ HTML
- `countKeywordOccurrences(String text, String keyword)`: Đếm số lần xuất hiện từ khóa
- `saveResultsToFile(String filename)`: Lưu kết quả vào file

## Cách sử dụng

### 1. Biên dịch chương trình
```bash
javac -d . src/search_engine/Search_Engine.java
```

### 2. Chạy chương trình
```bash
java search_engine.Search_Engine
```

### 3. Sử dụng menu
1. Chọn **1** để nhập website và độ sâu (ví dụ: https://example.com, độ sâu 2)
2. Chọn **2** để nhập từ khóa cần tìm (ví dụ: "java", "programming")
3. Chọn **3** để bắt đầu crawl và tìm kiếm
4. Chọn **4** để lưu kết quả vào file
5. Chọn **5** để thoát chương trình

## Ví dụ sử dụng
```
=== SEARCH ENGINE ===
1. Nhập địa chỉ website và độ sâu tìm kiếm
2. Nhập từ khóa cần tìm kiếm
3. Thực hiện crawl và tìm kiếm
4. Lưu kết quả vào file
5. Kết thúc chương trình
Chọn chức năng (1-5): 1

Nhập địa chỉ website ban đầu: https://example.com
Nhập độ sâu tìm kiếm (1-3): 2
Đã thiết lập:
- Website: https://example.com
- Độ sâu: 2
```

## File kết quả
Kết quả tìm kiếm sẽ được lưu vào file text với định dạng:
```
=== KẾT QUẢ TÌM KIẾM TỪ KHÓA: java ===
Tổng số trang tìm thấy: 5
Thời gian: Mon Oct 23 10:30:45 ICT 2023
==================================================

1. https://example.com/java-tutorial (Xuất hiện 15 lần)
2. https://example.com/programming (Xuất hiện 8 lần)
3. https://example.com/about (Xuất hiện 3 lần)
```

## Lưu ý
- Độ sâu tìm kiếm được giới hạn từ 1-3 để tránh crawl quá nhiều trang
- Chương trình giới hạn tối đa 100 trang để tránh quá tải
- Kết quả được sắp xếp theo số lần xuất hiện từ khóa giảm dần
- Chương trình tự động loại bỏ HTML tags khi đếm từ khóa
- Tìm kiếm không phân biệt chữ hoa/thường

## Yêu cầu hệ thống
- Java 8 trở lên
- Kết nối Internet để crawl website
