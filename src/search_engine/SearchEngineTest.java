package search_engine;

/**
 * Test class để kiểm tra các chức năng của Search Engine
 * <AUTHOR>
 */
public class SearchEngineTest {
    
    public static void main(String[] args) {
        System.out.println("=== TEST SEARCH ENGINE ===");
        
        // Test 1: Tạo Search Engine instance
        System.out.println("Test 1: Tạo Search Engine instance");
        Search_Engine searchEngine = new Search_Engine();
        System.out.println("✓ Tạo thành công Search Engine instance");
        
        // Test 2: Test countKeywordOccurrences method
        System.out.println("\nTest 2: Test đếm từ khóa");
        String testHtml = "<html><body><h1>Java Programming</h1><p>Java is a programming language. Java is popular.</p></body></html>";
        
        // Sử dụng reflection để test private method (chỉ để demo)
        try {
            java.lang.reflect.Method method = Search_Engine.class.getDeclaredMethod("countKeywordOccurrences", String.class, String.class);
            method.setAccessible(true);
            
            int count = (Integer) method.invoke(searchEngine, testHtml, "java");
            System.out.println("Số lần xuất hiện của 'java': " + count);
            System.out.println("✓ Test đếm từ khóa thành công");
            
        } catch (Exception e) {
            System.err.println("✗ Lỗi khi test đếm từ khóa: " + e.getMessage());
        }
        
        // Test 3: Test URL validation
        System.out.println("\nTest 3: Test validation URL");
        try {
            java.lang.reflect.Method method = Search_Engine.class.getDeclaredMethod("isValidUrl", String.class);
            method.setAccessible(true);
            
            boolean valid1 = (Boolean) method.invoke(searchEngine, "https://example.com");
            boolean valid2 = (Boolean) method.invoke(searchEngine, "http://example.com");
            boolean valid3 = (Boolean) method.invoke(searchEngine, "invalid-url");
            
            System.out.println("https://example.com: " + (valid1 ? "Valid" : "Invalid"));
            System.out.println("http://example.com: " + (valid2 ? "Valid" : "Invalid"));
            System.out.println("invalid-url: " + (valid3 ? "Valid" : "Invalid"));
            System.out.println("✓ Test validation URL thành công");
            
        } catch (Exception e) {
            System.err.println("✗ Lỗi khi test validation URL: " + e.getMessage());
        }
        
        // Test 4: Test menu display
        System.out.println("\nTest 4: Test hiển thị menu");
        Search_Engine.showMenu();
        System.out.println("✓ Test hiển thị menu thành công");
        
        System.out.println("\n=== KẾT THÚC TEST ===");
        System.out.println("Chương trình Search Engine đã sẵn sàng sử dụng!");
        System.out.println("Chạy lệnh: java search_engine.Search_Engine để bắt đầu");
    }
}
