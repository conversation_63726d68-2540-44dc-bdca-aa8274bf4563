package search_engine;

import java.io.*;
import java.net.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Search Engine đơn giản cho phép tìm kiếm từ khóa trên các trang web
 * <AUTHOR>
 */
public class Search_Engine {

    private Set<String> visitedUrls;
    private Map<String, Integer> searchResults;
    private String keyword;

    public Search_Engine() {
        this.visitedUrls = new HashSet<>();
        this.searchResults = new HashMap<>();
    }

    /**
     * Crawl website và thu thập các link theo độ sâu
     */
    public void crawlWebsite(String startUrl, int depth) {
        if (depth <= 0 || visitedUrls.contains(startUrl)) {
            return;
        }

        try {
            System.out.println("Đang crawl: " + startUrl);
            visitedUrls.add(startUrl);

            URL url = new URL(startUrl);
            URLConnection connection = url.openConnection();
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");

            BufferedReader reader = new BufferedReader(
                new InputStreamReader(connection.getInputStream())
            );

            StringBuilder content = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
            reader.close();

            String htmlContent = content.toString();

            // Tìm kiếm từ khóa trong nội dung
            if (keyword != null && !keyword.isEmpty()) {
                int count = countKeywordOccurrences(htmlContent, keyword);
                if (count > 0) {
                    searchResults.put(startUrl, count);
                }
            }

            // Tìm các link trong trang
            Set<String> links = extractLinks(htmlContent, startUrl);

            // Crawl các link con với độ sâu giảm dần
            for (String link : links) {
                if (visitedUrls.size() < 100) { // Giới hạn số lượng để tránh crawl quá nhiều
                    crawlWebsite(link, depth - 1);
                }
            }

        } catch (Exception e) {
            System.err.println("Lỗi khi crawl " + startUrl + ": " + e.getMessage());
        }
    }

    /**
     * Trích xuất các link từ HTML content
     */
    private Set<String> extractLinks(String htmlContent, String baseUrl) {
        Set<String> links = new HashSet<>();

        Pattern pattern = Pattern.compile("<a\\s+[^>]*href\\s*=\\s*[\"']([^\"']+)[\"'][^>]*>",
                                        Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(htmlContent);

        while (matcher.find()) {
            String link = matcher.group(1);
            String absoluteUrl = resolveUrl(baseUrl, link);
            if (absoluteUrl != null && isValidUrl(absoluteUrl)) {
                links.add(absoluteUrl);
            }
        }

        return links;
    }

    /**
     * Chuyển đổi relative URL thành absolute URL
     */
    private String resolveUrl(String baseUrl, String link) {
        try {
            if (link.startsWith("http://") || link.startsWith("https://")) {
                return link;
            }

            URL base = new URL(baseUrl);
            URL resolved = new URL(base, link);
            return resolved.toString();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Kiểm tra URL có hợp lệ không
     */
    private boolean isValidUrl(String url) {
        return url.startsWith("http://") || url.startsWith("https://");
    }

    /**
     * Đếm số lần xuất hiện của từ khóa trong text
     */
    private int countKeywordOccurrences(String text, String keyword) {
        if (text == null || keyword == null || keyword.isEmpty()) {
            return 0;
        }

        // Loại bỏ HTML tags
        String cleanText = text.replaceAll("<[^>]+>", " ").toLowerCase();
        String lowerKeyword = keyword.toLowerCase();

        int count = 0;
        int index = 0;
        while ((index = cleanText.indexOf(lowerKeyword, index)) != -1) {
            count++;
            index += lowerKeyword.length();
        }

        return count;
    }

    /**
     * Tìm kiếm từ khóa trong các URL đã crawl
     */
    public void searchKeyword(String searchKeyword) {
        this.keyword = searchKeyword;
        this.searchResults.clear();

        System.out.println("Đang tìm kiếm từ khóa: " + keyword);

        // Crawl lại các URL đã thu thập để tìm từ khóa
        Set<String> urlsToSearch = new HashSet<>(visitedUrls);
        for (String url : urlsToSearch) {
            try {
                URL urlObj = new URL(url);
                URLConnection connection = urlObj.openConnection();
                connection.setRequestProperty("User-Agent", "Mozilla/5.0");

                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream())
                );

                StringBuilder content = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
                reader.close();

                int count = countKeywordOccurrences(content.toString(), keyword);
                if (count > 0) {
                    searchResults.put(url, count);
                }

            } catch (Exception e) {
                System.err.println("Lỗi khi tìm kiếm trong " + url + ": " + e.getMessage());
            }
        }
    }

    /**
     * Lưu kết quả tìm kiếm vào file, sắp xếp theo số lần xuất hiện giảm dần
     */
    public void saveResultsToFile(String filename) {
        try {
            // Sử dụng TreeMap với Comparator để sắp xếp theo giá trị giảm dần
            Map<String, Integer> sortedResults = new TreeMap<>(new Comparator<String>() {
                @Override
                public int compare(String url1, String url2) {
                    int count1 = searchResults.get(url1);
                    int count2 = searchResults.get(url2);

                    // Sắp xếp theo số lần xuất hiện giảm dần
                    int result = Integer.compare(count2, count1);

                    // Nếu số lần xuất hiện bằng nhau, sắp xếp theo URL
                    if (result == 0) {
                        result = url1.compareTo(url2);
                    }

                    return result;
                }
            });

            sortedResults.putAll(searchResults);

            PrintWriter writer = new PrintWriter(new FileWriter(filename));
            writer.println("=== KẾT QUẢ TÌM KIẾM TỪ KHÓA: " + keyword + " ===");
            writer.println("Tổng số trang tìm thấy: " + sortedResults.size());
            writer.println("Thời gian: " + new Date());
            writer.println("=" + "=".repeat(50));
            writer.println();

            int rank = 1;
            for (Map.Entry<String, Integer> entry : sortedResults.entrySet()) {
                writer.printf("%d. %s (Xuất hiện %d lần)%n",
                            rank++, entry.getKey(), entry.getValue());
            }

            writer.close();
            System.out.println("Đã lưu kết quả vào file: " + filename);
            System.out.println("Tổng số trang tìm thấy từ khóa: " + sortedResults.size());

        } catch (IOException e) {
            System.err.println("Lỗi khi lưu file: " + e.getMessage());
        }
    }

    /**
     * Hiển thị menu chương trình
     */
    public static void showMenu() {
        System.out.println("\n=== SEARCH ENGINE ===");
        System.out.println("1. Nhập địa chỉ website và độ sâu tìm kiếm");
        System.out.println("2. Nhập từ khóa cần tìm kiếm");
        System.out.println("3. Thực hiện crawl và tìm kiếm");
        System.out.println("4. Lưu kết quả vào file");
        System.out.println("5. Kết thúc chương trình");
        System.out.print("Chọn chức năng (1-5): ");
    }

    /**
     * Main method - điểm bắt đầu của chương trình
     */
    public static void main(String[] args) {
        Search_Engine searchEngine = new Search_Engine();
        Scanner scanner = new Scanner(System.in);

        String startUrl = "";
        int depth = 0;
        String keyword = "";
        boolean hasSearched = false;

        System.out.println("Chào mừng bạn đến với Search Engine!");

        while (true) {
            showMenu();

            try {
                int choice = scanner.nextInt();
                scanner.nextLine(); // Consume newline

                switch (choice) {
                    case 1:
                        System.out.print("Nhập địa chỉ website ban đầu: ");
                        startUrl = scanner.nextLine().trim();

                        System.out.print("Nhập độ sâu tìm kiếm (1-3): ");
                        depth = scanner.nextInt();
                        scanner.nextLine(); // Consume newline

                        if (depth < 1 || depth > 3) {
                            System.out.println("Độ sâu phải từ 1 đến 3!");
                            break;
                        }

                        System.out.println("Đã thiết lập:");
                        System.out.println("- Website: " + startUrl);
                        System.out.println("- Độ sâu: " + depth);
                        break;

                    case 2:
                        System.out.print("Nhập từ khóa cần tìm kiếm: ");
                        keyword = scanner.nextLine().trim();

                        if (keyword.isEmpty()) {
                            System.out.println("Từ khóa không được để trống!");
                            break;
                        }

                        System.out.println("Đã thiết lập từ khóa: " + keyword);
                        break;

                    case 3:
                        if (startUrl.isEmpty() || depth == 0) {
                            System.out.println("Vui lòng nhập website và độ sâu trước!");
                            break;
                        }

                        if (keyword.isEmpty()) {
                            System.out.println("Vui lòng nhập từ khóa trước!");
                            break;
                        }

                        System.out.println("Bắt đầu crawl và tìm kiếm...");
                        searchEngine.keyword = keyword;
                        searchEngine.crawlWebsite(startUrl, depth);

                        System.out.println("\nKết quả crawl:");
                        System.out.println("- Tổng số trang đã crawl: " + searchEngine.visitedUrls.size());
                        System.out.println("- Số trang chứa từ khóa: " + searchEngine.searchResults.size());

                        hasSearched = true;
                        break;

                    case 4:
                        if (!hasSearched || searchEngine.searchResults.isEmpty()) {
                            System.out.println("Chưa có kết quả tìm kiếm nào để lưu!");
                            break;
                        }

                        System.out.print("Nhập tên file để lưu kết quả (ví dụ: results.txt): ");
                        String filename = scanner.nextLine().trim();

                        if (filename.isEmpty()) {
                            filename = "search_results_" + System.currentTimeMillis() + ".txt";
                        }

                        searchEngine.saveResultsToFile(filename);
                        break;

                    case 5:
                        System.out.println("Cảm ơn bạn đã sử dụng Search Engine!");
                        scanner.close();
                        System.exit(0);
                        break;

                    default:
                        System.out.println("Lựa chọn không hợp lệ! Vui lòng chọn từ 1-5.");
                        break;
                }

            } catch (InputMismatchException e) {
                System.out.println("Vui lòng nhập số hợp lệ!");
                scanner.nextLine(); // Clear invalid input
            } catch (Exception e) {
                System.err.println("Đã xảy ra lỗi: " + e.getMessage());
            }
        }
    }
}
